from modules.pinterest_uploader import PinterestUploader
import os
from datetime import datetime
import requests
from concurrent.futures import ThreadPoolExecutor
from modules.parser import Parser
from modules.downloader import Downloader

proxy = {
    'http': 'http://ihlvwajj-rotate:<EMAIL>:80/',
    'https': 'socks5://ihlvwajj-rotate:<EMAIL>:80/'
}

requests.get(
        "https://ipv4.webshare.io/",
        proxies=proxy
).text

# If you want to work through a proxy, specify the proxy in the format: TYPE://LOGIN:PASSWORD@IP:PORT
# 10 FREE proxies: https://www.webshare.io/?referral_code=fp27vdieqruw

# Define your scheduling plan
schedule_plan = {
 datetime(2024, 1, 14): ['08:00', '10:00', '12:00', '14:00', '16:00'],
 datetime(2024, 1, 15): ['08:00', '10:00', '12:00', '14:00', '16:00'],
 datetime(2024, 1, 16): ['08:00', '10:00', '12:00', '14:00', '16:00'],
 datetime(2024, 1, 17): ['08:00', '10:00', '12:00', '14:00', '16:00'],
 datetime(2024, 1, 18): ['08:00', '10:00', '12:00', '14:00', '16:00'],
 datetime(2024, 1, 19): ['08:00', '10:00', '12:00', '14:00', '16:00'],  
 
# Repeat for the next day
# Add more dates as needed
}

def parse_keyword(key, email, password, proxy):
    parser = Parser(email, password, proxy=proxy, headless=False)
    parser.login()
    parser.parse_by_keyword(key, max_links=2000)

def parse_keyword_group(keywords, email, password, proxy):
    parser = Parser(email, password, proxy=proxy, headless=False)
    parser.parse_multiple_keywords(keywords)

def parsing(keywords):
    email = '<EMAIL>'
    password = 'Ppa_Tt_2024!!'
    proxy = {
        'http': 'http://ihlvwajj-rotate:<EMAIL>:80/',
        'https': 'socks5://ihlvwajj-rotate:<EMAIL>:80/'
    }

    # Split keywords into groups of 3 (or however many concurrent processes you want)
    keyword_groups = [keywords[i:i+3] for i in range(0, len(keywords), 3)]

    with ThreadPoolExecutor(max_workers=3) as executor:
        executor.map(lambda group: parse_keyword_group(group, email, password, proxy), keyword_groups)

def download_for_keyword(key):
    downloader = Downloader(key, proxy=proxy, headless=False)
    links = downloader.read_links_file()

    for index, link in enumerate(links, 1):
        print(f"Processing link {index}/{len(links)} for {key}")
        try:
            downloader.download(link)
        except Exception as e:
            print(f"Error processing link {link} for {key}: {str(e)}")
        print(f"Moving to next link for {key}...")

def downloading(keywords):
    for key in keywords:
        downloader = Downloader(key)
        downloader.start_downloads()

def schedule_videos(uploader, folder_path, schedule_plan):
    videos = sorted(os.listdir(folder_path))
    video_index = 0

    for date, times in schedule_plan.items():
        for time_str in times:
            if video_index >= len(videos):
                break  # Stop if we have scheduled all videos

            video_file = videos[video_index]
            video_file_path = os.path.join(folder_path, video_file)
            schedule_datetime = datetime.combine(date, datetime.strptime(time_str, "%H:%M").time())

            #append username to description
            description = f"Spring nails are the nails for me! I can't wait for spring to come around! credit: @{PinterestUploader.extract_username(video_file)}"

            # Call the upload_video method
            uploader.upload_video(
                "Spring Nails", #board Name 
                "Spring Nails", # title
                description, # duh
                video_file_path, # duh
                "", # link
                
                "", # tagged topics as a string
                #TOPICS NEEDS UPDATED, YOU MUST PRESS ENTER AFTER EACH TOPIC IS TYPED IN
                schedule_datetime #yuh
            )

            video_index += 1

def upload_to_pinterest():
    folder_path = "path/to/your/videos"  # Replace with the path to your videos folder
    email = "<EMAIL>"  # Replace with your Pinterest email
    password = "Ccn_Pt_2023!!"  # Replace with your Pinterest password

    uploader = PinterestUploader(email, password, headless=None)
    uploader.login_to_pinterest()
    schedule_videos(uploader, r"C:\Users\<USER>\Desktop\test nails", schedule_plan)

def remove_duplicates(keywords):
    all_links = set()
    duplicates_removed = 0

    for keyword in keywords:
        folder_path = os.path.join('results', keyword)
        file_path = os.path.join(folder_path, f'{keyword}.txt')
        
        if not os.path.exists(file_path):
            print(f"File not found for keyword: {keyword}")
            continue

        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            links = content.replace('https://', '\nhttps://').split('\n')
        
        unique_links = []
        for link in links:
            link = link.strip()
            if link.startswith('https://') and link not in all_links:
                all_links.add(link)
                unique_links.append(link)
            elif link.startswith('https://'):
                duplicates_removed += 1

        with open(file_path, 'w', encoding='utf-8') as file:
            for link in unique_links:
                file.write(f"{link}\n")

        print(f"Processed {keyword}: {len(unique_links)} unique links")

    print(f"Total duplicates removed across all keywords: {duplicates_removed}")

if __name__ == '__main__':
    print("Choose an option:")
    print("1. Parse TikTok")
    print("2. Download Videos")
    print("3. Upload to Pinterest")
    print("4. Remove Duplicate Links")
    
    choice = input("Enter your choice (1/2/3/4): ")

    if choice == '1':
        keywords = input("Enter keywords separated by commas: ").split(',')
        parsing(keywords)
    elif choice == '2':
        keywords = input("Enter keywords separated by commas: ").split(',')
        downloading(keywords)
    elif choice == '3':
        upload_to_pinterest()
    elif choice == '4':
        keywords = input("Enter keywords to process (separated by commas): ").split(',')
        remove_duplicates(keywords)
    else:
        print("Invalid choice. Please run the script again and choose 1, 2, 3, or 4.")
