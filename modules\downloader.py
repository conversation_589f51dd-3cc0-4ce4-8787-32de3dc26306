import os
import re
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor, as_completed
from selenium.webdriver.common.by import By
from modules.base import TikTok
import yt_dlp

class Downloader(TikTok):
    URL = 'https://snaptik.app/'

    def __init__(self, key, proxy=None, headless=False, max_concurrent_downloads=5):
        super().__init__(proxy, headless)
        self.video_save_path = os.path.join(self.results_path, key)
        self.links_file_path = os.path.join(self.video_save_path, f'{key}.txt')
        self.max_concurrent_downloads = max_concurrent_downloads

        # Ensure that the folders exist
        os.makedirs(self.video_save_path, exist_ok=True)

    def read_links_file(self):
        if not os.path.isfile(self.links_file_path):
            print(f"File '{self.links_file_path}' not found.")
            return []

        with open(self.links_file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
            if not lines:
                print(f"File '{self.links_file_path}' is empty.")
                return []

        return [line.strip() for line in lines]

    async def download_video(self, link):
        try:
            username = link.split('/')[3].lstrip('@')
            video_id = link.split("/")[-1]

            safe_username = re.sub(r'[^\w\-_\. ]', '_', username)
            safe_video_id = re.sub(r'[^\w\-_\. ]', '_', video_id)

            filename = f'{safe_username}_{safe_video_id}.mp4'
            file_save_path = os.path.join(self.video_save_path, filename)

            ydl_opts = {
                'outtmpl': file_save_path,
                'quiet': True,
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([link])

            print(f'Successfully downloaded: {filename}')
            return True
        except Exception as e:
            print(f"Error downloading video from {link}: {str(e)}")
            return False

    async def download_all(self):
        links = self.read_links_file()
        tasks = []

        async with aiohttp.ClientSession() as session:
            for link in links:
                task = asyncio.ensure_future(self.download_video(link))
                tasks.append(task)

            results = await asyncio.gather(*tasks)

        successful_downloads = sum(results)
        print(f"Downloaded {successful_downloads} out of {len(links)} videos.")

    def start_downloads(self):
        asyncio.run(self.download_all())
