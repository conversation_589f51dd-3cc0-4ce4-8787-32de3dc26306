from modules.base import <PERSON><PERSON><PERSON><PERSON>
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import NoSuchElementException
import os
import time
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from datetime import datetime
from selenium.common.exceptions import TimeoutException


class PinterestUploader(TikTok):
    PINTEREST_LOGIN_URL = "https://www.pinterest.com/login/"

    def __init__(self, email, password, proxy=None, headless=None):
        super().__init__(proxy, headless)
        self.email = email
        self.password = password

    def login_to_pinterest(self):
        print("Logging into Pinterest...")
        self.driver.get(self.PINTEREST_LOGIN_URL)

        # Find and fill in the email input field
        input_email = self._wait_for_element_located(By.NAME, "id")
        input_email.send_keys(self.email)

        # Find and fill in the password input field
        input_password = self._wait_for_element_located(By.NAME, "password")
        input_password.send_keys(self.password)

        # Find and click the login button
        login_button = self._wait_for_element_clickable(By.CSS_SELECTOR, 'button[type="submit"]')
        login_button.click()

        # Wait for some time to ensure login is successful
        self._wait_for_element_invisible(By.CSS_SELECTOR, 'button[type="submit"]', timeout=10)

    def navigate_to_pin_creation(self):
        print("Navigating to Pin creation page...")
        self.driver.get("https://www.pinterest.com/pin-creation-tool/")
        WebDriverWait(self.driver, 10).until(EC.visibility_of_element_located((By.CSS_SELECTOR, 'div[data-test-id="header-content-container"]')))

    def handle_alert(self):
        try:
            alert = WebDriverWait(self.driver, 10).until(EC.alert_is_present())
            alert.accept()  # or alert.dismiss() if you want to cancel the alert
            print("Alert found and accepted")
        except TimeoutException:
            print("No alert found")

#    @staticmethod
#    def extract_username(video_file_path):
#        # Extracts the username from the video file name
#        filename = os.path.basename(video_file_path)
#        username_parts = filename.split('_')
#        # Handle usernames with a period at the beginning or multiple underscores
#        if username_parts[0] == '':
#            username = '_' + username_parts[1]
#        else:
#            username = username_parts[0]
#            return username

    @staticmethod
    def extract_username(video_file_path):
        # Extracts the username from the video file name
        filename = os.path.basename(video_file_path)
        # Split the filename at underscores
        parts = filename.split('_')
        # Join all parts before the string of numbers (which is followed by an underscore)
        username = '_'.join(parts[:-2])
        return username


    def upload_video(self, board_name, title, description, video_file_path, link, tagged_topics, schedule_datetime):
        #Handle any alert that might appear
        self.handle_alert()
        
        self.navigate_to_pin_creation()
        #self.select_or_create_board(board_name)

        # Wait until a certain element is visible before proceeding
        WebDriverWait(self.driver, 10).until(EC.visibility_of_element_located((By.CSS_SELECTOR, 'div[data-test-id="create-modal-left-header-section"]')))

        # Select the Board #unnecessary
        #self._wait_for_element_clickable(By.CSS_SELECTOR, '[title="Select"]').click()
        #self._wait_for_element_clickable(By.XPATH, f"//div[text()='{board_name}']").click()

        # Upload the Video
        print("Uploading video...")
        upload_input = self.driver.find_element(By.CSS_SELECTOR, '#storyboard-upload-input')
        upload_input.send_keys(os.path.abspath(video_file_path))
        
        # Wait for the video to upload
        time.sleep(5)  # Adjust this time based on your video size and internet speed

        # Set the Title
        title_input = self._wait_for_element_clickable(By.CSS_SELECTOR, '#storyboard-selector-title')
        title_input.send_keys(title)

        # Set the Description
        description_input = self._wait_for_element_clickable(By.XPATH, '//div[@aria-label="Add a detailed description"]')
        description_input.send_keys(description)

        #Set the Link
        link_input = self._wait_for_element_clickable(By.XPATH, '//*[@id="WebsiteField"]')
        link_input.send_keys(link)

        #Set the tagged topics
        tagged_topics_element = self._wait_for_element_clickable(By.XPATH, '//*[@id="storyboard-selector-interest-tags"]')
        tagged_topics_element.send_keys(tagged_topics)

        # Set the Schedule
        self.set_schedule_date(schedule_datetime)
        self.set_schedule_time(schedule_datetime)

        # Schedule the Pin
        schedule_pin = self._wait_for_element_clickable(
            By.CSS_SELECTOR, 'div[data-test-id="storyboard-creation-nav-done"] > button'
        )
        schedule_pin.click()

        #confirm schedule the pin
        self.confirm_schedule()

    def confirm_schedule(self):
        # Find the confirmation schedule button using the CSS selector and click it
         confirm_button = self._wait_for_element_clickable(
             By.CSS_SELECTOR, 'div[data-test-id="schedule-pin-confirm-button"] > button'
        )
         confirm_button.click()

         time.sleep(5) # Wait for the pin to complete scheduling

    # Wait for the pin to complete scheduling
#         WebDriverWait(self.driver, 10).until(EC.visibility_of_element_located((By.CSS_SELECTOR, 'div[data-test-id="storyboard-creation-nav-done"]')))

#    def confirm_schedule(self):
#        try:
#            # Find the confirmation schedule button using the CSS selector and click it
#            confirm_button = self._wait_for_element_clickable(By.CSS_SELECTOR, 'button[data-test-id="schedule-pin-confirm-button"]')
#            confirm_button.click()
#            print("Schedule confirmed")
#        except NoSuchElementException:
#            print("Confirm schedule button not found")

    def set_schedule_date(self, schedule_date):

        # Check the checkbox before setting the date
        checkbox = self.driver.find_element(By.ID,'pin-draft-switch-group')
        if not checkbox.is_selected():
            checkbox.click()

        # Click the date input field to open the date picker
        date_input = self.driver.find_element(By.CSS_SELECTOR, 'input[type="text"][placeholder="MM/DD/YYYY"]')
        date_input.click()

        date_picker = self._wait_for_element_clickable(By.CSS_SELECTOR, 'div.react-datepicker-wrapper')
        date_picker.click()

        date_label = "Choose " + schedule_date.strftime("%A, %B %dth, %Y")
        date_selector = f"div[aria-label='{date_label}']"
        self._wait_for_element_clickable(By.CSS_SELECTOR, date_selector).click()

    def set_schedule_time(self, schedule_time):
        time_picker = self._wait_for_element_clickable(By.CSS_SELECTOR, 'input[placeholder="Time"]')
        time_picker.click()

        time_index = (schedule_time.hour * 2) + (schedule_time.minute // 30)
        time_selector = f"#time-field-dropdown-item-{time_index}"
        self._wait_for_element_clickable(By.CSS_SELECTOR, time_selector).click()

    def select_or_create_board(self, board_name):
        try:
            # Open the board dropdown
            self._wait_for_element_clickable(By.CSS_SELECTOR, 'button[data-test-id="board-dropdown-select-button"]').click()
            
            # Construct the selector for the specific board
            board_selector = f"div[title='{board_name}']"
            
            # Wait for the board to be clickable and then click it
            self._wait_for_element_clickable(By.CSS_SELECTOR, board_selector).click()
        except NoSuchElementException:
            # If the board doesn't exist, create it
            print(f"Board '{board_name}' not found. Creating new board...")
            self.create_new_board(board_name)

    def create_new_board(self, board_name):
        # Click on the option to create a new board
        # Replace the selector with the actual one for Pinterest's "Create Board" button
        create_board_button = self._wait_for_element_clickable(By.CSS_SELECTOR, 'div[data-test-id="create-board"]')
        create_board_button.click()

        # Wait for the new board modal/dialog to open
        # Replace the selector with the actual one for the new board name input field
        new_board_name_input = self._wait_for_element_clickable(By.XPATH, '//*[@id="boardEditName"]')
        new_board_name_input.send_keys(board_name)

        # Optional: Set any additional board settings here

        # Submit the new board creation
        # Replace the selector with the actual one for the "Create" button
        create_button = self._wait_for_element_clickable(By.CSS_SELECTOR, 'button[type="submit"]')
        create_button.click()

        time.sleep(5) # Wait for the board to be created

        # Post the Pin
        post_button = self._wait_for_element_clickable(By.CSS_SELECTOR, 'div[data-test-id="storyboard-creation-nav-done"]')
        post_button.click()

        print("Video uploaded successfully!")